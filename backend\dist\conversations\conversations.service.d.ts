import { Repository } from 'typeorm';
import { Conversation } from './conversation.entity';
export declare class ConversationsService {
    private conversationsRepository;
    constructor(conversationsRepository: Repository<Conversation>);
    findAll(): Promise<Conversation[]>;
    findOne(id: string): Promise<Conversation>;
    findByStoreId(storeId: string): Promise<Conversation[]>;
    findByUserId(userId: string): Promise<Conversation[]>;
    findByUuid(uuid: string): Promise<Conversation>;
    create(createConversationDto: Partial<Conversation> & {
        userId?: string | number;
    }): Promise<Conversation>;
    update(id: string, updateConversationDto: Partial<Conversation>): Promise<Conversation>;
    remove(id: string): Promise<void>;
}
