import {
  Controller,
  Get,
  Post,
  Body,
  Patch,
  Param,
  Delete,
  Query,
} from '@nestjs/common';
import { ApiTags, ApiOperation, ApiResponse } from '@nestjs/swagger';
import { ConversationsService } from './conversations.service';
import { Conversation } from './conversation.entity';

@ApiTags('conversations')
@Controller('conversations')
export class ConversationsController {
  constructor(private readonly conversationsService: ConversationsService) {}

  @Post()
  @ApiOperation({ summary: 'Create a new conversation' })
  @ApiResponse({ status: 201, description: 'Conversation created successfully' })
  @ApiResponse({ status: 400, description: 'Bad request' })
  create(@Body() createConversationDto: Partial<Conversation> & { userId?: string | number }) {
    return this.conversationsService.create(createConversationDto);
  }

  @Get()
  @ApiOperation({ summary: 'Get all conversations' })
  @ApiResponse({ status: 200, description: 'Conversations retrieved successfully' })
  findAll() {
    return this.conversationsService.findAll();
  }

  @Get('store/:storeId')
  @ApiOperation({ summary: 'Get conversations by store ID' })
  @ApiResponse({ status: 200, description: 'Conversations retrieved successfully' })
  findByStoreId(@Param('storeId') storeId: string) {
    return this.conversationsService.findByStoreId(storeId);
  }

  @Get('user/:userId')
  @ApiOperation({ summary: 'Get conversations by user ID' })
  @ApiResponse({ status: 200, description: 'Conversations retrieved successfully' })
  findByUserId(@Param('userId') userId: string) {
    return this.conversationsService.findByUserId(userId);
  }

  @Get('uuid/:uuid')
  @ApiOperation({ summary: 'Get conversation by UUID' })
  @ApiResponse({ status: 200, description: 'Conversation retrieved successfully' })
  @ApiResponse({ status: 404, description: 'Conversation not found' })
  findByUuid(@Param('uuid') uuid: string) {
    return this.conversationsService.findByUuid(uuid);
  }

  @Get(':id')
  @ApiOperation({ summary: 'Get a conversation by ID' })
  @ApiResponse({ status: 200, description: 'Conversation retrieved successfully' })
  @ApiResponse({ status: 404, description: 'Conversation not found' })
  findOne(@Param('id') id: string) {
    return this.conversationsService.findOne(id);
  }

  @Patch(':id')
  @ApiOperation({ summary: 'Update a conversation' })
  @ApiResponse({ status: 200, description: 'Conversation updated successfully' })
  @ApiResponse({ status: 404, description: 'Conversation not found' })
  update(@Param('id') id: string, @Body() updateConversationDto: Partial<Conversation>) {
    return this.conversationsService.update(id, updateConversationDto);
  }

  @Delete(':id')
  @ApiOperation({ summary: 'Delete a conversation' })
  @ApiResponse({ status: 200, description: 'Conversation deleted successfully' })
  @ApiResponse({ status: 404, description: 'Conversation not found' })
  remove(@Param('id') id: string) {
    return this.conversationsService.remove(id);
  }
}
