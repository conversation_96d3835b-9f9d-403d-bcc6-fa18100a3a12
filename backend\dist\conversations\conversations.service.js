"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var __param = (this && this.__param) || function (paramIndex, decorator) {
    return function (target, key) { decorator(target, key, paramIndex); }
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.ConversationsService = void 0;
const common_1 = require("@nestjs/common");
const typeorm_1 = require("@nestjs/typeorm");
const typeorm_2 = require("typeorm");
const conversation_entity_1 = require("./conversation.entity");
let ConversationsService = class ConversationsService {
    constructor(conversationsRepository) {
        this.conversationsRepository = conversationsRepository;
    }
    async findAll() {
        return this.conversationsRepository.find({
            where: { isDeleted: false },
            order: { createdAt: 'DESC' },
        });
    }
    async findOne(id) {
        const conversation = await this.conversationsRepository.findOne({
            where: { id, isDeleted: false },
        });
        if (!conversation) {
            throw new common_1.NotFoundException(`Conversation with ID ${id} not found`);
        }
        return conversation;
    }
    async findByStoreId(storeId) {
        return this.conversationsRepository.find({
            where: { storeId, isDeleted: false },
            order: { createdAt: 'DESC' },
        });
    }
    async findByUserId(userId) {
        return this.conversationsRepository.find({
            where: { userId, isDeleted: false },
            order: { createdAt: 'DESC' },
        });
    }
    async findByUuid(uuid) {
        const conversation = await this.conversationsRepository.findOne({
            where: { uuid, isDeleted: false },
        });
        if (!conversation) {
            throw new common_1.NotFoundException(`Conversation with UUID ${uuid} not found`);
        }
        return conversation;
    }
    async create(createConversationDto) {
        const { userId, ...conversationData } = createConversationDto;
        const conversation = this.conversationsRepository.create({
            ...conversationData,
            createdBy: userId?.toString(),
        });
        return this.conversationsRepository.save(conversation);
    }
    async update(id, updateConversationDto) {
        const conversation = await this.findOne(id);
        Object.assign(conversation, updateConversationDto);
        return this.conversationsRepository.save(conversation);
    }
    async remove(id) {
        const conversation = await this.findOne(id);
        conversation.isDeleted = true;
        await this.conversationsRepository.save(conversation);
    }
};
exports.ConversationsService = ConversationsService;
exports.ConversationsService = ConversationsService = __decorate([
    (0, common_1.Injectable)(),
    __param(0, (0, typeorm_1.InjectRepository)(conversation_entity_1.Conversation)),
    __metadata("design:paramtypes", [typeorm_2.Repository])
], ConversationsService);
//# sourceMappingURL=conversations.service.js.map