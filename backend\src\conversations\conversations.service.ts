import { Injectable, NotFoundException } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository } from 'typeorm';
import { Conversation } from './conversation.entity';

@Injectable()
export class ConversationsService {
  constructor(
    @InjectRepository(Conversation)
    private conversationsRepository: Repository<Conversation>,
  ) {}

  async findAll(): Promise<Conversation[]> {
    return this.conversationsRepository.find({
      where: { isDeleted: false },
      order: { createdAt: 'DESC' },
    });
  }

  async findOne(id: string): Promise<Conversation> {
    const conversation = await this.conversationsRepository.findOne({
      where: { id, isDeleted: false },
    });
    
    if (!conversation) {
      throw new NotFoundException(`Conversation with ID ${id} not found`);
    }
    
    return conversation;
  }

  async findByStoreId(storeId: string): Promise<Conversation[]> {
    return this.conversationsRepository.find({
      where: { storeId, isDeleted: false },
      order: { createdAt: 'DESC' },
    });
  }

  async findByUserId(userId: string): Promise<Conversation[]> {
    return this.conversationsRepository.find({
      where: { userId, isDeleted: false },
      order: { createdAt: 'DESC' },
    });
  }

  async findByUuid(uuid: string): Promise<Conversation> {
    const conversation = await this.conversationsRepository.findOne({
      where: { uuid, isDeleted: false },
    });
    
    if (!conversation) {
      throw new NotFoundException(`Conversation with UUID ${uuid} not found`);
    }
    
    return conversation;
  }

  async create(createConversationDto: Partial<Conversation> & { userId?: string | number }): Promise<Conversation> {
    // Extract userId and map it to the correct fields
    const { userId, ...conversationData } = createConversationDto;

    // Create the conversation with proper field mapping
    const conversation = this.conversationsRepository.create({
      ...conversationData,
      createdBy: userId?.toString(),
    });

    return this.conversationsRepository.save(conversation);
  }

  async update(id: string, updateConversationDto: Partial<Conversation>): Promise<Conversation> {
    const conversation = await this.findOne(id);
    Object.assign(conversation, updateConversationDto);
    return this.conversationsRepository.save(conversation);
  }

  async remove(id: string): Promise<void> {
    const conversation = await this.findOne(id);
    conversation.isDeleted = true;
    await this.conversationsRepository.save(conversation);
  }
}
